#include "update_test_widget.h"

#include <X11/Xlib.h>

#include "GI_graphheaddef.h"
#include "IGCommApp.h"
#include "baseconst.h"
#include "front_lib.h"
#include "typedef.h"
#include "mainwindow.h"
#include "common.h"

namespace WangXuan {
int GetAnswer;
}
int m_wait_time;

CDbMap cd;

bool convertAllPic() {
  qDebug() << "convertAllPic";

  QProgressDialog* progressDialog = new QProgressDialog(NULL);
  progressDialog->setWindowModality(Qt::WindowModal);
  progressDialog->setWindowTitle(QObject::tr("Please Wait"));
  progressDialog->setLabelText(QObject::tr("Copying..."));
  progressDialog->setCancelButtonText(QObject::tr("Cancel"));

  QString dir_str = Common::storePath() + "/config/";
  QDir file_dir(dir_str);
  if (!file_dir.exists()) {
    if (file_dir.mkpath(dir_str))
      qDebug() << "画面信息文件路径已创建，路径为：" << dir_str;
  }
QString cmdStr = QString("rm %1/config/allGraphInfo.e").arg(Common::storePath());
  system(cmdStr.toLocal8Bit().constData());
  QFile export_file(dir_str + QString("allGraphInfo.e"));
  if (!export_file.open(QFile::WriteOnly | QFile::Text)) {
    QMessageBox::warning(NULL, QObject::tr("警告"),
                         QObject::tr("全站图形信息文件导出失败，请检查"));
    delete progressDialog;
    return false;
  }

  QList<INT32> yk_totol_list;
  QList<INT32> yk_repeat_list;
  bool repeat_yk_flag = false;

  QTextStream out(&export_file);
  out.setCodec("UTF-8");

  QString head =
      QString("<!Entity==画面文件信号 Version=1.0 Code=UTF-8 time='%1'!>\n")
          .arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
  out << head;
  //<变电站>
  QString substation = QObject::tr("<Substation::变电站>\n");
  out << substation;

  // QString substation_desc = QObject::tr("@序号 变电站名称 变电站描述\n");
  QString substation_desc = QObject::tr("@序号 变电站name 变电站desc\n");
  out << substation_desc;

  INT32 tmp_size = 8;
  CHAR* tmp_buf = (CHAR*)malloc(tmp_size);

  INT32 RetNum = GetAllRec(SCADA, FAC_INFO_NO, tmp_buf, tmp_size);
  if (RetNum >= 0) {
    struct fac_info_scada* pStru = (fac_info_scada*)tmp_buf;
    for (INT32 m = 0; m < RetNum; m++) {
      QString fac_info = QString("#%1 %2 %3\n")
                             .arg(m + 1)
                             .arg(pStru[m].fac_alias)
                             .arg(pStru[m].fac_name);
      out << fac_info;
    }
  }
  free(tmp_buf);

  QString substation_end = QObject::tr("</Substation>\n");
  out << substation_end;

  QDir dir(GRAPHDIR);
  dir.setFilter(QDir::Files);
  QStringList filters;
  filters << "*.pic";
  dir.setNameFilters(filters);
  dir.setSorting(QDir::Name);
  QFileInfoList flist = dir.entryInfoList();

  int graph_index = 1;
  int node_index = 1;

  progressDialog->setRange(0, flist.count());
  progressDialog->resize(400, 200);
  progressDialog->show();

  for (int i = 0; i < flist.count(); i++) {
    QString filename = flist.at(i).absoluteFilePath();
    if (filename.isEmpty() == true) continue;

    QFile file(filename);
    if (file.open(QIODevice::ReadOnly) == false) continue;

    //qDebug() << "filename" << filename;

    progressDialog->setValue(i);
    progressDialog->setLabelText(QString("当前文件名：%1").arg(filename));
    if (progressDialog->wasCanceled()) {
      delete progressDialog;
      return false;
    }

    QDataStream ar(&file);
    ar.setByteOrder(QDataStream::LittleEndian);

    CGraphEnv* pEnv = NULL;
    CGraphHead* pHead = NULL;

    QString fileType = filename.right(ICONTYPE.size());

    if (fileType == GRAPHTYPE) {
      // qDebug()<<"GRAPHTYPE";
      pEnv = new CGraphEnv;
      pHead = new CGraphHead;
      pEnv->SetEnvType(GI_ENVTYPE_GRAPHEDIT);
    } else {
      continue;
    }

    pEnv->SetGraphHead(pHead);
    pHead->SetGraphEnv(pEnv);

    pEnv->CreateCommApp();
    pEnv->InitEnv();
    pHead->Reset();
    pHead->Serialize(ar, FALSE);
    file.close();

    QString graph_start = QObject::tr("<Graph::图形>\n");
    out << graph_start;
    QString pic_desc = QObject::tr("@序号 图形名称 图形描述\n");
    out << pic_desc;

    QString pic_file_name = flist.at(i).baseName();
    QString pic_info =
        QString("#%1 GRAPH%2 %3\n").arg(1).arg(graph_index).arg(pic_file_name);
    out << pic_info;
    qDebug() << "check graph_index" << graph_index << pic_file_name;
    graph_index++;

    QString node = QObject::tr("<Object::测点>\n");
    out << node;

    QString node_desc =
        QObject::tr("@序号 signalID signaltype reference x y desc\n");
    out << node_desc;

    QStringList node_str;
    pHead->getNodeInfo(node_str);
    // node_list.append(node_str);

    for (int j = 0; j < node_str.count(); j++) {
      QString node_info = QString("#%1 %2").arg(node_index).arg(node_str.at(j));
      node_index++;
      qDebug() << node_info;
      out << node_info;
    }

    QString node_end = QObject::tr("</Object::测点>\n");
    out << node_end;

    QStringList yk_str;
    QList<INT32> yk_list;
    QList<INT32> m_cb_list;
    pHead->getYkInfo(yk_str, yk_list, m_cb_list);
    // yk_list.append(yk_str);
    for (int j = 0; j < yk_list.count(); j++) {
      if (yk_totol_list.contains(yk_list.at(j))) {
        repeat_yk_flag = true;
        // break;
        yk_repeat_list.append(yk_list.at(j));
      } else
        yk_totol_list.append(yk_list.at(j));
    }

    QString yk = QObject::tr("<Object::遥控>\n");
    out << yk;

    QString yk_desc = QObject::tr("@序号 reference desc associatedPoint\n");
    out << yk_desc;

    for (int j = 0; j < yk_str.count(); j++) {
      QString yk_info = QString("#%1 %2").arg(node_index).arg(yk_str.at(j));
      node_index++;
      qDebug() << yk_info;
      out << yk_info;
    }

    QString yk_end = QObject::tr("</Object::遥控>\n");
    out << yk_end;

    out << QObject::tr("</Graph>\n");

    delete pEnv;
    delete pHead;
  }

  out.flush();
  export_file.close();
  delete progressDialog;

  if (repeat_yk_flag) {
    QString yk_str;
    for (int i = 0; i < yk_repeat_list.count(); i++) {
      char yk_name[256];
      cd.GetNameByIdx(yk_repeat_list.at(i), yk_name, 256);
      yk_str += QString(yk_name);
      if (i != yk_repeat_list.count() - 1) yk_str += QString(";");
    }

    QMessageBox::information(
        NULL, QObject::tr("警告"),
        QObject::tr("全站图形信息文件导出结束，文件导出为%1/config/"
                    "allGraphInfo.e,文件包含重复遥控(%1),请检查！")
            .arg(Common::storePath()).arg(yk_str));
    return true;
  }

  QMessageBox::information(
      NULL, QObject::tr("告知"),
      QObject::tr("全站图形信息文件导出结束，文件导出为%1/config/allGraphInfo.e").arg(Common::storePath()));
}

bool convertSinglePic() {
  qDebug() << "convertSinglePic";

  QProgressDialog* progressDialog = new QProgressDialog(NULL);
  progressDialog->setWindowModality(Qt::WindowModal);

  progressDialog->setWindowTitle(QObject::tr("Please Wait"));
  progressDialog->setLabelText(QObject::tr("Copying..."));
  progressDialog->setCancelButtonText(QObject::tr("Cancel"));

  QString dir_str = Common::storePath()+"/config/";
  QDir file_dir(dir_str);
  if (!file_dir.exists()) {
    if (file_dir.mkpath(dir_str))
      qDebug() << "画面信息文件路径已创建，路径为：" << dir_str;
  }

  QString cmdStr=QString("rm %1/config/GRAPH*").arg(Common::storePath());
  system(cmdStr.toLocal8Bit().constData());

  QDir dir(GRAPHDIR);
  dir.setFilter(QDir::Files);
  QStringList filters;
  filters << "*.pic";
  dir.setNameFilters(filters);
  dir.setSorting(QDir::Time | QDir::Reversed);
  QFileInfoList flist = dir.entryInfoList();

  int graph_index = 1;
  int node_index = 1;

  progressDialog->setRange(0, flist.count());
  progressDialog->resize(400, 200);
  progressDialog->show();

  for (int i = 0; i < flist.count(); i++) {
    QString filename = flist.at(i).absoluteFilePath();
    if (filename.isEmpty() == true) continue;

    QFile file(filename);
    if (file.open(QIODevice::ReadOnly) == false) continue;

    //qDebug() << "filename" << filename;

    progressDialog->setValue(i);
    progressDialog->setLabelText(QString("当前文件名：%1").arg(filename));
    if (progressDialog->wasCanceled()) {
      delete progressDialog;
      return false;
    }

    QDataStream ar(&file);
    ar.setByteOrder(QDataStream::LittleEndian);

    IGraphEnv* pEnv = NULL;
    IGraphHead* pHead = NULL;

    QString fileType = filename.right(ICONTYPE.size());
    if (fileType == GRAPHTYPE) {
      // qDebug()<<"GRAPHTYPE";
      pEnv = new CGraphEnv;
      pHead = new CGraphHead;
      pEnv->SetEnvType(GI_ENVTYPE_GRAPHEDIT);
    } else {
      continue;
    }

    pEnv->SetGraphHead(pHead);
    pHead->SetGraphEnv(pEnv);

    pEnv->CreateCommApp();
    pEnv->InitEnv();
    pHead->Reset();
    pHead->Serialize(ar, FALSE);
    file.close();

    QString pic_file_name = flist.at(i).baseName();

    qDebug() << "check graph_index" << graph_index << pic_file_name;
    pHead->writerToSigInfoFile(pic_file_name, graph_index, node_index);
    graph_index++;

    delete pEnv;
    delete pHead;
  }

  delete progressDialog;
  return true;
}

QString changeYkRef(QString ref) {
  QStringList tmp_list = ref.split(".");
  if (tmp_list.count() > 1) {
    QString ref1 = tmp_list.at(0);
    QString result_ref = ref1 + "CTRL.";
    for (int i = 1; i < tmp_list.count(); i++) {
      result_ref += tmp_list.at(i);
      if (i != tmp_list.count() - 1) {
        result_ref += ".";
      }
    }

    return result_ref;
  }

  return ref;
}

int get_yk_keyword(struct yk_order_table_scada& ykkey) {
  TableOp yk_table(SCADA, YK_ORDER_TABLE_NO);
  int retcode;

  retcode = yk_table.TableGet((char*)&ykkey, (char*)&ykkey, sizeof(ykkey));

  if (retcode < 0) {
    ykkey.yk_id = 0;
    yk_table.TableClose();
    return (-1);
  } else {
    yk_table.TableClose();
    return (0);
  }
}

int GetLdNoByYkId(int yk_id, short& ld_no, short& order_no) {
  if (yk_id / 1000000 != YK_YX_RELY_NO) return (-1);

  TableOp TablePtr;
  int ret = TablePtr.TableOpen(SCADA, YK_YX_RELY_NO);
  if (ret < 0) return (-1);

  int size = sizeof(yk_yx_rely_scada);
  char* tmpbuf;
  tmpbuf = (char*)malloc(size * sizeof(char));
  ret = TablePtr.TableGet((char*)&yk_id, tmpbuf, size);
  if (ret < 0) {
    free(tmpbuf);
    TablePtr.TableClose();
    return (-1);
  }
  TablePtr.TableClose();
  struct yk_yx_rely_scada* p_stru = (yk_yx_rely_scada*)tmpbuf;

  yk_channel_scada channel_stru;
  char* buffer = (char*)malloc(sizeof(yk_channel_scada));
  memcpy(buffer, (char*)&(p_stru->yk_id), sizeof(int));
  memcpy(buffer + sizeof(int), (char*)&(p_stru->current_channel), sizeof(char));
  free(tmpbuf);

  ret = TablePtr.TableOpen(SCADA, YK_CHANNEL_NO);
  if (ret < 0) {
    free(buffer);
    return (-1);
  }
  ret = TablePtr.TableGet((char*)buffer, (char*)&channel_stru,
                          sizeof(yk_channel_scada));
  if (ret < 0) {
    free(buffer);
    TablePtr.TableClose();
    return (-1);
  }

  order_no = channel_stru.order_no;
  CDbMap cd;
  ret = cd.GetNoByAlias(channel_stru.ld_alias, &ld_no);

  free(buffer);
  TablePtr.TableClose();
  return (0);
}

int sendYkCheckOrder(const char* ykAlias, int value) {
  printf("start yk check %s-%d\n", ykAlias, value);
  struct yk_struct yk;
  struct yk_order_table_scada ykkey;
  struct yk_channel_scada yk_channel;

  CDbMap dbmap;
  COrderMap ordermap;

  char yk_alias[256];
  strcpy(yk_alias, ykAlias);
  int yk_id = dbmap.GetIdxByAlias(yk_alias);
  short ld_no;
  short order_no;

  if (GetLdNoByYkId(yk_id, ld_no, order_no) < 0) {
    printf("can not find ld no!\n");
    return -1;
  }
  yk.flag = 0;
  yk.type = YK_SET_CMD_TYPE;
  yk.fac_no = ld_no;
  yk.order = order_no;
  yk.status = value;
  yk.process_no = 0;
  yk.yk_time = 0;
  yk.pad1 = 0;
  yk.pad2 = 0;

  yk.dev_id = 0;
  yk.yk_id = 0;
  yk.channel = 0;
  yk.gin = 0;
  yk.exec_type = 0;

  ykkey.ld_no = ld_no;
  ykkey.dot_no = order_no;
  if (get_yk_keyword(ykkey) < 0) return -1;

  yk.yk_id = ykkey.yk_id;
  yk.channel = ykkey.channel;

  if (ordermap.GetYkChannel(yk.yk_id, yk.channel, &yk_channel) < 0) return -1;

  yk.gin = yk_channel.gin;
  yk.exec_type = yk_channel.exec_type;
  yk.dev_id = dbmap.GetDevIdxByIdx(ykkey.yk_id);

  int ret = save_yk_command_buffer(1, &yk);

  printf(
      "YK CHECK:ret=%d fac=%03d order=%04d oper=%x type=%x(dev_id=%d yk_id=%d "
      "channel=%d gin=%d exec=%d)\n",
      ret, yk.fac_no, yk.order, yk.status, yk.type, yk.dev_id, yk.yk_id,
      yk.channel, yk.gin, yk.exec_type);

  return ret;
}

int sendYkExecOrder(const char* ykAlias, int value) {
  printf("start yk %s-%d\n", ykAlias, value);
  struct yk_struct yk;
  struct yk_order_table_scada ykkey;
  struct yk_channel_scada yk_channel;

  CDbMap dbmap;
  COrderMap ordermap;

  char yk_alias[256];
  strcpy(yk_alias, ykAlias);
  int yk_id = dbmap.GetIdxByAlias(yk_alias);
  short ld_no;
  short order_no;

  if (GetLdNoByYkId(yk_id, ld_no, order_no) < 0) {
    printf("can not find ld no!\n");
    return -1;
  }
  yk.flag = 0;
  yk.type = YK_EXE_CMD_TYPE;
  yk.fac_no = ld_no;
  yk.order = order_no;
  yk.status = value;
  yk.process_no = 0;
  yk.yk_time = 0;
  yk.pad1 = 0;
  yk.pad2 = 0;

  yk.dev_id = 0;
  yk.yk_id = 0;
  yk.channel = 0;
  yk.gin = 0;
  yk.exec_type = 0;

  ykkey.ld_no = ld_no;
  ykkey.dot_no = order_no;
  if (get_yk_keyword(ykkey) < 0) return -1;

  yk.yk_id = ykkey.yk_id;
  yk.channel = ykkey.channel;

  if (ordermap.GetYkChannel(yk.yk_id, yk.channel, &yk_channel) < 0) return -1;

  yk.gin = yk_channel.gin;
  yk.exec_type = yk_channel.exec_type;
  yk.dev_id = dbmap.GetDevIdxByIdx(ykkey.yk_id);

  int ret = save_yk_command_buffer(1, &yk);

  printf(
      "YK EXE: ret=%d fac=%03d order=%04d oper=%x type=%x(dev_id=%d yk_id=%d "
      "channel=%d gin=%d exec=%d)\n",
      ret, yk.fac_no, yk.order, yk.status, yk.type, yk.dev_id, yk.yk_id,
      yk.channel, yk.gin, yk.exec_type);

  return ret;
}

BOOL32 CheckYkReturn(int DevId, unsigned char NewState) {
  QString str;

  if (DevId / 1000000 == CB_DEVICE_NO) {
    struct cb_device_scada cb_stru;
    INT32 ret = LdbGetRecordById(CB_DEVICE_NO, (CHAR*)&DevId, (CHAR*)&cb_stru,
                                 sizeof(cb_device_scada));
    if (ret >= 0) {
      if (cb_stru.state == NewState) {
        return TRUE;
      }
    }
  }

  return FALSE;
}

void showTables(QTableWidget* m_table_widget,
                QMap<INT32, yk_op_stru> show_map) {
  m_table_widget->setColumnCount(3);
  m_table_widget->setColumnWidth(0, 600);
  m_table_widget->setEditTriggers(
      QAbstractItemView::NoEditTriggers);  // 设置为不可编辑

  m_table_widget->setFrameStyle(QFrame::NoFrame);

  m_table_widget->setHorizontalHeaderItem(
      0, new QTableWidgetItem(QObject::tr("遥控名称")));
  m_table_widget->setHorizontalHeaderItem(
      1, new QTableWidgetItem(QObject::tr("控制方式")));
  m_table_widget->setHorizontalHeaderItem(
      2, new QTableWidgetItem(QObject::tr("结果")));

  int row = 0;
  QMapIterator<INT32, yk_op_stru> i(show_map);
  while (i.hasNext()) {
    i.next();
    if (i.value().yk_ref == "null") continue;
    row++;
  }
  m_table_widget->setRowCount(row);

  QMapIterator<INT32, yk_op_stru> i2(show_map);
  row = 0;
  while (i2.hasNext()) {
    i2.next();
    if (i2.value().yk_ref == "null") continue;
    if (i2.value().is_check == 0) continue;
    int state = i2.value().state;
    int result = i2.value().result;

    QTableWidgetItem* item0 = new QTableWidgetItem(i2.value().yk_name);
    item0->setTextAlignment(Qt::AlignHCenter | Qt::AlignVCenter);
    item0->setToolTip(i2.value().yk_ref);
    m_table_widget->setItem(row, 0, item0);

    if (state == 1) {
      QTableWidgetItem* item1 = new QTableWidgetItem(QString("合"));
      item1->setTextAlignment(Qt::AlignHCenter | Qt::AlignVCenter);
      m_table_widget->setItem(row, 1, item1);
    } else {
      QTableWidgetItem* item1 = new QTableWidgetItem(QString("分"));
      item1->setTextAlignment(Qt::AlignHCenter | Qt::AlignVCenter);
      m_table_widget->setItem(row, 1, item1);
    }

    if (result == 1) {
      QTableWidgetItem* item2 = new QTableWidgetItem(QString("成功"));
      item2->setTextAlignment(Qt::AlignHCenter | Qt::AlignVCenter);
      m_table_widget->setItem(row, 2, item2);
    } else {
      QTableWidgetItem* item2 = new QTableWidgetItem(QString("失败"));
      item2->setTextAlignment(Qt::AlignHCenter | Qt::AlignVCenter);
      m_table_widget->setItem(row, 2, item2);
    }

    row++;
  }
}

void getOpenYkId(int DevId, int& ope_yk_id) {
  ope_yk_id = -1;
  INT32 bufsize = 8;
  CHAR* buf = (CHAR*)malloc(bufsize * sizeof(CHAR));
  INT32 num = cd.GetAllRecordByDev(YK_YX_RELY_NO, DevId, buf, bufsize);
  QString yk_ref;
  if (num <= 0) {
    free(buf);
    return;
  }

  for (INT32 j = 0; j < num; j++) {
    INT32 yk_id = ((INT32*)buf)[j];

    if (yk_id <= 0) continue;

    struct yk_yx_rely_scada pStru;
    if (LdbGetRecordById(YK_YX_RELY_NO, (CHAR*)&yk_id, (CHAR*)&pStru,
                         sizeof(yk_yx_rely_scada)) >= 0) {
      if (pStru.sync_mode == 3) {
        ope_yk_id = yk_id;
        break;
      }
    }
  }

  free(buf);
}

void checkYk(QFile& file, QMap<INT32, yk_op_stru>& yk_map) {
  QMap<INT32, QString> all_ref_map;

  if (InitApp(SCADA) >= 0) {
    int tmp_size = 8;
    char* tmp_buf = (CHAR*)malloc(tmp_size);

    INT32 RetNum2 = GetAllRec(SCADA, CHANNEL_REFERENCE_NO, tmp_buf, tmp_size);
    if (RetNum2 >= 0) {
      struct channel_reference_scada* pStru = (channel_reference_scada*)tmp_buf;
      for (INT32 m = 0; m < RetNum2; m++) {
        all_ref_map.insert(pStru[m].id, pStru[m].ref);
      }
    }
    free(tmp_buf);
  }

  QTextStream out(&file);
  out.setCodec("UTF-8");

  QString head =
      QString("<!Entity=遥控记录文件 time='%1' Version=1.0 Code=UTF-8 !>\n")
          .arg(QDateTime::currentDateTime().toString("yyyy_MM_dd_hh_mm_ss"));
  out << head;

  QString substation = QObject::tr("<Substation::变电站>\n");
  out << substation;

  QString substation_desc = QObject::tr("@序号 变电站名称 变电站描述\n");
  out << substation_desc;

  INT32 tmp_size = 8;
  CHAR* tmp_buf = (CHAR*)malloc(tmp_size);

  INT32 RetNum = GetAllRec(SCADA, FAC_INFO_NO, tmp_buf, tmp_size);
  if (RetNum >= 0) {
    struct fac_info_scada* pStru = (fac_info_scada*)tmp_buf;
    for (INT32 m = 0; m < RetNum; m++) {
      QString fac_info = QString("#%1 %2 %3\n")
                             .arg(m + 1)
                             .arg(pStru[m].fac_alias)
                             .arg(pStru[m].fac_name);
      out << fac_info;
    }
  }
  free(tmp_buf);

  QString substation_end = QObject::tr("</Substation>\n");
  out << substation_end;

  QString yk = QObject::tr("<Object::遥控>\n");
  out << yk;

  QString yk_desc =
      QObject::tr("@序号 reference desc value success error_reason time\n");
  out << yk_desc;

  QSettings settings("graph_update_test.ini", QSettings::IniFormat);
  int yk_check_time = settings.value("setting/yk_check_time", 5).toInt();
  qDebug() << "yk_check_time" << yk_check_time;

  int yk_index = 1;

  QMapIterator<INT32, yk_op_stru> i(yk_map);
  while (i.hasNext()) {
    i.next();

    int yk_id = i.key();
    int dev_id = i.value().dev_id;

    qDebug() << "check yk_id" << yk_id;
    struct cb_device_scada cb_stru;
    if (LdbGetRecordById(CB_DEVICE_NO, (CHAR*)&dev_id, (CHAR*)&cb_stru,
                         sizeof(cb_device_scada)) <= 0)
      continue;
    int new_value = 1;
    // 如果开关状态为分，目标状态为合
    if (cb_stru.state == 0) {
      new_value = 1;
    } else {
      new_value = 0;
    }
    int value = i.value().sync_mode;
    qDebug() << "check value1" << value << new_value;

    if ((value & (0x01 << 1) != 0) && (value & (0x01 << 2) != 0))
      value = 3;
    else if ((value & (0x01 << 1) != 0) && (value & (0x01 << 2) == 0))
      value = 1;
    else if ((value & (0x01 << 1) != 0) && (value & (0x01 << 2) == 0))
      value = 2;
    qDebug() << "check value2" << value;

    if (value >= 1 && value <= 3) {
      if (value == 3 || (value == 1 && new_value == 1) ||
          (value == 2 && new_value == 0)) {
        value = new_value;
      } else {
        yk_map[i.key()].is_check = 0;
        continue;
      }
    } else if (value >= 4 && value <= 128) {
      if (value == 4) {
        value = 2;
      }
      if (value == 8) {
        value = 3;
      }
      if (value == 16) {
        value = 4;
      }
      if (value == 32) {
        value = 5;
      }
      if (value == 64) {
        value = 6;
      }
    } else {
      yk_map[i.key()].is_check = 0;
      continue;
    }

    qDebug() << "check value3" << value;

    struct yk_yx_rely_scada pStru;
    if (LdbGetRecordById(YK_YX_RELY_NO, (CHAR*)&yk_id, (CHAR*)&pStru,
                         sizeof(yk_yx_rely_scada)) <= 0)
      continue;
    INT32 i2 = pStru.yk_type;
    unsigned char current_channel = pStru.current_channel;

    QString yk_return_str;
    QString yk_ref = i.value().yk_ref;
    qDebug() << "i2" << i2 << "yk_id" << yk_id << "dev_id" << dev_id << "value"
             << value << "yk_ref" << yk_ref;
    // yk_ref = changeYkRef(yk_ref);
    if (yk_ref == "null") continue;

    char yk_alias[64];
    cd.GetAliasByIdx(yk_id, yk_alias, 64);

    if ((value == 2 || value == 3 || value == 4 || value == 5 || value == 6) &&
        (new_value == 0)) {
      char open_yk_alias[64];
      int open_yk_id = -1;
      getOpenYkId(dev_id, open_yk_id);

      if (open_yk_id < 0) {
        qDebug() << "getOpenYkId error";
        continue;
      }
      cd.GetAliasByIdx(open_yk_id, open_yk_alias, 64);
      QString tmp_yk_ref = all_ref_map.value(open_yk_id);
      char tmp_yk_name[256];
      cd.GetNameByIdx(open_yk_id, tmp_yk_name, 256);
      if (i2 != 2) {
        QString time_str_tmp =
            QDateTime::currentDateTime().toString("yyyy-MM-dd_hh:mm:ss:zzz");
        QString sbow_ref = tmp_yk_ref;
        sbow_ref.replace(".ctlVal", ".SBOw");
        if (sendYkCheckOrder(open_yk_alias, 0) < 0) {
          qDebug() << "sendYkCheckOrder open error";

          yk_return_str = QString("#%1 %2 %3 %4 false - %5\n")
                              .arg(yk_index)
                              .arg(sbow_ref)
                              .arg(tmp_yk_name)
                              .arg(0)
                              .arg(time_str_tmp);
          out << yk_return_str;
          yk_index++;
          continue;
        } else {
          yk_return_str = QString("#%1 %2 %3 %4 true success %5\n")
                              .arg(yk_index)
                              .arg(sbow_ref)
                              .arg(tmp_yk_name)
                              .arg(0)
                              .arg(time_str_tmp);
          out << yk_return_str;
          yk_index++;
        }
      }

      if (sendYkExecOrder(open_yk_alias, 0) < 0) {
        QString op_ref = tmp_yk_ref;
        op_ref.replace(".ctlVal", ".Oper");
        qDebug() << "sendYkExecOrder open error";
        QString time_str_tmp =
            QDateTime::currentDateTime().toString("yyyy-MM-dd_hh:mm:ss:zzz");
        yk_return_str = QString("#%1 %2 %3 %4 false - %5\n")
                            .arg(yk_index)
                            .arg(op_ref)
                            .arg(tmp_yk_name)
                            .arg(0)
                            .arg(time_str_tmp);
        out << yk_return_str;
        yk_index++;
        continue;
      }

      int check_index = 0;
      while (!CheckYkReturn(dev_id, 0)) {
        check_index++;
        qDebug() << "tongqi check_index" << check_index;

        if (check_index == yk_check_time * 10) break;
        usleep(1000 * 100);
        // QApplication::processEvents();
      }
      if (check_index == yk_check_time * 10) {
        QString op_ref = tmp_yk_ref;
        op_ref.replace(".ctlVal", ".Oper");
        QString time_str_tmp =
            QDateTime::currentDateTime().toString("yyyy-MM-dd_hh:mm:ss:zzz");
        yk_return_str = QString("#%1 %2 %3 %4 false - %5\n")
                            .arg(yk_index)
                            .arg(op_ref)
                            .arg(tmp_yk_name)
                            .arg(0)
                            .arg(time_str_tmp);

      } else {
        QString op_ref = tmp_yk_ref;
        op_ref.replace(".ctlVal", ".Oper");

        QString time_str_tmp =
            QDateTime::currentDateTime().toString("yyyy-MM-dd_hh:mm:ss:zzz");
        yk_return_str = QString("#%1 %2 %3 %4 true success %5\n")
                            .arg(yk_index)
                            .arg(op_ref)
                            .arg(tmp_yk_name)
                            .arg(0)
                            .arg(time_str_tmp);
        new_value = 1;
      }
      yk_index++;
      out << yk_return_str;
    }
    yk_map[i.key()].state = new_value;
    // i2 == 2为直控
    if (i2 != 2) {
      // if (!SendReport(YK_PREV_RECORD, dev_id, yk_id, current_channel,
      // value))
      // {

      QString sbow_ref = yk_ref;
      sbow_ref.replace(".ctlVal", ".SBOw");
      qDebug() << "sbow_ref" << sbow_ref;
      if (sendYkCheckOrder(yk_alias, value) < 0) {
        QString time_str_tmp =
            QDateTime::currentDateTime().toString("yyyy-MM-dd_hh:mm:ss:zzz");
        yk_return_str = QString("#%1 %2 %3 %4 false - %5\n")
                            .arg(yk_index)
                            .arg(sbow_ref)
                            .arg(i.value().yk_name)
                            .arg(0)
                            .arg(time_str_tmp);
        out << yk_return_str;
        yk_index++;
        yk_map[i.key()].result = 0;
        continue;
      } else {
        QString time_str_tmp =
            QDateTime::currentDateTime().toString("yyyy-MM-dd_hh:mm:ss:zzz");
        yk_return_str = QString("#%1 %2 %3 %4 true success %5\n")
                            .arg(yk_index)
                            .arg(sbow_ref)
                            .arg(i.value().yk_name)
                            .arg(new_value)
                            .arg(time_str_tmp);
        out << yk_return_str;
        yk_index++;
      }
    }
    // usleep(1000 * 1000);

    qDebug() << "i.value().yk_name" << i.value().yk_name;
    QString op_ref = yk_ref;
    op_ref.replace(".ctlVal", ".Oper");
    // if (!SendReport(YK_RECORD, dev_id, yk_id, current_channel, value)) {

    if (sendYkExecOrder(yk_alias, value) < 0) {
      QString time_str_tmp =
          QDateTime::currentDateTime().toString("yyyy-MM-dd_hh:mm:ss:zzz");
      yk_return_str = QString("#%1 %2 %3 %4 false - %5\n")
                          .arg(yk_index)
                          .arg(op_ref)
                          .arg(i.value().yk_name)
                          .arg(new_value)
                          .arg(time_str_tmp);
      out << yk_return_str;
      yk_index++;
      yk_map[i.key()].result = 0;
      continue;
    }
    int check_index = 0;

    char dev_name[256];
    cd.GetNameByIdx(dev_id, dev_name, 256);
    qDebug() << "check cb" << dev_name;
    while (!CheckYkReturn(dev_id, new_value)) {
      check_index++;

      qDebug() << "check_index" << check_index;

      // progress_dialog->setLabelText(
      //     QString("(%1) %2
      //     等待遥控返回").arg(yk_index).arg(i.value().yk_name));

      if (check_index == yk_check_time * 10) break;
      usleep(1000 * 100);
      // QApplication::processEvents();
    }
    QString time_str_tmp =
        QDateTime::currentDateTime().toString("yyyy-MM-dd_hh:mm:ss:zzz");
    if (check_index == yk_check_time * 10) {
      yk_return_str = QString("#%1 %2 %3 %4 false - %5\n")
                          .arg(yk_index)
                          .arg(op_ref)
                          .arg(i.value().yk_name)
                          .arg(new_value)
                          .arg(time_str_tmp);
      yk_map[i.key()].result = 0;
    } else {
      yk_return_str = QString("#%1 %2 %3 %4 true success %5\n")
                          .arg(yk_index)
                          .arg(op_ref)
                          .arg(i.value().yk_name)
                          .arg(new_value)
                          .arg(time_str_tmp);
      yk_map[i.key()].result = 1;
    }
    yk_index++;
    out << yk_return_str;
    qDebug() << yk_return_str;
  }

  QString yk_end = QObject::tr("</Object::遥控>\n");
  out << yk_end;

  out.flush();
  file.close();
}

CPicSimuYkWidget::CPicSimuYkWidget(QWidget* parent) {
  QVBoxLayout* m_v_layout = new QVBoxLayout();
  m_v_layout->setContentsMargins(0, 0, 0, 0);
  m_v_layout->setSpacing(10);

  m_table_widget = new QTableWidget(this);
  m_table_widget->setRowCount(0);

  PicThread* thread = new PicThread();
  thread->start();

  WangXuan::GetAnswer = 0;
  WangXuan::WaitDlg* dlg = new WangXuan::WaitDlg(600);
  dlg->exec();

  MainWindow *mainWindow=(MainWindow *)parent;
  if(mainWindow)
  {
      QString para4_OptType=QString::fromLocal8Bit("分图遥控");
      QString para5_LogInfo=QString::fromLocal8Bit("导出记录%1").arg(Common::storePath()+"/data/yklog_AllGraph.e");
      QStringList logList=QStringList() << Common::getDateTime()<< para4_OptType<< para5_LogInfo;
      mainWindow->outputLog(logList);
  }

  showTables(m_table_widget, thread->yk_pic_map);

  m_v_layout->addWidget(m_table_widget);

  setLayout(m_v_layout);
}

CPicSimuYkWidget::~CPicSimuYkWidget() { close_result_buf(); }

CDbSimuYkWidget::CDbSimuYkWidget(QWidget* parent) {
  QVBoxLayout* m_v_layout = new QVBoxLayout();
  m_v_layout->setContentsMargins(0, 0, 0, 0);
  m_v_layout->setSpacing(10);

  m_table_widget = new QTableWidget(this);
  m_table_widget->setRowCount(0);

  DbThread* thread = new DbThread();
  thread->start();

  WangXuan::GetAnswer = 0;
  WangXuan::WaitDlg* dlg = new WangXuan::WaitDlg(600);
  dlg->exec();

  MainWindow *mainWindow=(MainWindow *)parent;
  if(mainWindow)
  {
      QString para4_OptType=QString::fromLocal8Bit("全站遥控");
      QString para5_LogInfo=QString::fromLocal8Bit("导出记录%1").arg(Common::storePath()+"/data/yklog_AllDbInfo.e");
      QStringList logList=QStringList() << Common::getDateTime()<< para4_OptType<< para5_LogInfo;
      mainWindow->outputLog(logList);
  }

  showTables(m_table_widget, thread->yk_db_map);

  m_v_layout->addWidget(m_table_widget);

  setLayout(m_v_layout);
}

CDbSimuYkWidget::~CDbSimuYkWidget() { close_result_buf(); }

WangXuan::WaitDlg::WaitDlg(QWidget* parent) : QDialog(parent) {
  time_out = 30;
  time_out = m_wait_time;
  theTimeFlag = 0;
  setWindowTitle(tr("请等待(%1)...").arg(time_out));
  setMinimumSize(200, 80);
  setFixedSize(200, 80);

  OverFlag = NULL;
  timer = 0;
  timeover = false;
  QString per_msg = QString("正在等待");
  msg = per_msg + msg;
  // 设置时钟间隔
  startTimer(100);  // 单位为 毫秒
  QHBoxLayout* lay = new QHBoxLayout;

  GetAnswer = false;
  pic = new QLabel(this);
  //	QMovie *mov = new QMovie(":progress.gif",QByteArray(),pic);
  //	pic->setMovie(mov);
  pic->setText(msg);
  //	mov->start();
  lay->addWidget(pic, 0, Qt::AlignCenter);
  setLayout(lay);
  setModal(true);
  // setWindowFlags(Qt::WindowTitleHint);
}

WangXuan::WaitDlg::WaitDlg(int TimOut, QWidget* parent) : QDialog(parent) {
  time_out = TimOut;
  theTimeFlag = 0, setWindowTitle(tr("请等待(%1)...").arg(time_out));
  setMinimumSize(200, 80);
  setFixedSize(200, 80);

  timer = 0;
  timeover = false;
  OverFlag = NULL;
  QString per_msg = QString("请等待");
  msg = per_msg + msg;
  // 设置时钟间隔
  startTimer(100);  // 单位为 毫秒
  QHBoxLayout* lay = new QHBoxLayout;

  GetAnswer = false;

  pic = new QLabel(this);
  //	QMovie *mov = new QMovie(":progress.gif",QByteArray(),pic);
  //	pic->setMovie(mov);
  pic->setText(msg);
  //	mov->start();
  lay->addWidget(pic, 0, Qt::AlignCenter);
  setLayout(lay);
  setModal(true);
  // setWindowFlags(Qt::WindowTitleHint);
  // run();
}

void WangXuan::WaitDlg::Quit() { close(); }

void WangXuan::WaitDlg::timerEvent(QTimerEvent* e) {
  timer++;
  if (timer % 10 == 0) {
    theTimeFlag++;
    setWindowTitle(tr("请等待(%1)...").arg(time_out - theTimeFlag));
    if (time_out == theTimeFlag) {
      timeover = true;
      emit Quit();
    }
    if (GetAnswer) {
      emit Quit();
    }
  }
}

PicThread::PicThread() {}

void PicThread::run() {
  QString dir_str = Common::storePath()+"/data/";
  QDir file_dir(dir_str);
  if (!file_dir.exists()) {
    if (file_dir.mkpath(dir_str))
      qDebug() << "画面信息文件路径已创建，路径为：" << dir_str;
  }

  QString filename = QString("%1/yklog_AllGraph.e").arg(dir_str);
  QFile yk_file(filename);
  if (!yk_file.open(QFile::WriteOnly | QFile::Text)) {
    QApplication::restoreOverrideCursor();
    printf("Cannot write file %s : %s\n", filename.toAscii().data(),
           yk_file.errorString().toAscii().data());
    return;
  }

  QMap<INT32, QString> all_ref_map;

  if (InitApp(SCADA) >= 0) {
    int tmp_size = 8;
    char* tmp_buf = (CHAR*)malloc(tmp_size);

    INT32 RetNum2 = GetAllRec(SCADA, CHANNEL_REFERENCE_NO, tmp_buf, tmp_size);
    if (RetNum2 >= 0) {
      struct channel_reference_scada* pStru = (channel_reference_scada*)tmp_buf;
      for (INT32 m = 0; m < RetNum2; m++) {
        all_ref_map.insert(pStru[m].id, pStru[m].ref);
      }
    }
    free(tmp_buf);
  }

  QDir dir(GRAPHDIR);
  dir.setFilter(QDir::Files);
  QStringList filters;
  filters << "*.pic";
  dir.setNameFilters(filters);
  dir.setSorting(QDir::Time | QDir::Reversed);
  QFileInfoList flist = dir.entryInfoList();

  yk_pic_list.clear();
  yk_pic_map.clear();

  qDebug() << "flist.count()" << flist.count();

  for (int i = 0; i < flist.count(); i++) {
    QString filename = flist.at(i).absoluteFilePath();
    if (filename.isEmpty() == true) continue;

    QFile file(filename);
    if (file.open(QIODevice::ReadOnly) == false) continue;

    qDebug() << "filename" << filename;

    // progressDialog->setLabelText(QString("读取图形：%1").arg(filename));

    QDataStream ar(&file);
    ar.setByteOrder(QDataStream::LittleEndian);

    IGraphEnv* pEnv = NULL;
    IGraphHead* pHead = NULL;

    QString fileType = filename.right(ICONTYPE.size());
    if (fileType == ICONTYPE) {
      pEnv = new CIconEnv;
      pHead = new CIconHead;
      pEnv->SetEnvType(GI_ENVTYPE_ICONEDIT);
    } else if (fileType == GRAPHTYPE) {
      // qDebug()<<"GRAPHTYPE";
      pEnv = new CGraphEnv;
      pHead = new CGraphHead;
      pEnv->SetEnvType(GI_ENVTYPE_GRAPHEDIT);
    } else {
      continue;
    }

    pEnv->SetGraphHead(pHead);
    pHead->SetGraphEnv(pEnv);

    // pEnv->CreateCommApp();
    pEnv->InitEnv();
    pHead->Reset();
    pHead->Serialize(ar, FALSE);
    file.close();

    QStringList yk_str;
    QList<INT32> yk_list;
    QList<INT32> m_cb_list;

    pHead->getYkInfo(yk_str, yk_list, m_cb_list);
    for (int i = 0; i < yk_list.count(); i++) {
      if (!yk_pic_list.contains(yk_list.at(i))) {
        yk_pic_list.append(yk_list.at(i));
      }
    }

    if (pEnv) {
      delete pEnv;
      pEnv = NULL;
    }

    // usleep(1000 * 100);
  }

  for (int i = 0; i < yk_pic_list.count(); i++) {
    yk_op_stru tmp_op_stru;
    tmp_op_stru.yk_id = yk_pic_list.at(i);

    char yk_name[256];
    cd.GetNameByIdx(yk_pic_list.at(i), yk_name, 256);
    strcpy(tmp_op_stru.yk_name, yk_name);

    int dev_id = cd.GetDevIdxByIdx(yk_pic_list.at(i));
    tmp_op_stru.dev_id = dev_id;

    if (all_ref_map.contains(tmp_op_stru.yk_id)) {
      tmp_op_stru.yk_ref = all_ref_map.value(tmp_op_stru.yk_id);
    } else {
      tmp_op_stru.yk_ref = "null";
    }

    struct yk_yx_rely_scada pStru;
    if (LdbGetRecordById(YK_YX_RELY_NO, (CHAR*)&tmp_op_stru.yk_id,
                         (CHAR*)&pStru, sizeof(yk_yx_rely_scada)) >= 0) {
      tmp_op_stru.sync_mode = pStru.sync_mode;
      if (tmp_op_stru.sync_mode == 0) continue;
    }

    tmp_op_stru.is_check = 1;
    yk_pic_map.insert(tmp_op_stru.yk_id, tmp_op_stru);
  }

  qDebug() << "----------------------------------------------------------------"
              "---------------start YK count"
           << yk_pic_map.count();

  open_result_buf();
  checkYk(yk_file, yk_pic_map);

  QString file_make_time =
      QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
  QString cmdStr=QString("cp %1/data/yklog_AllGraph.e "
                 "%1/data/yklog_AllGraph_%s.e").arg(Common::storePath());
  char cmd[256];
  sprintf(cmd,
          cmdStr.toLocal8Bit().constData(),
          file_make_time.toAscii().data());
  qDebug() << "cmd" << cmd;
  system(cmd);
  WangXuan::GetAnswer = 1;

  yk_file.close();
}

DbThread::DbThread() {}

void DbThread::run() {
  QString dir_str = Common::storePath()+"/data/";
  QDir file_dir(dir_str);
  if (!file_dir.exists()) {
    if (file_dir.mkpath(dir_str))
      qDebug() << "画面信息文件路径已创建，路径为：" << dir_str;
  }

  QString filename = QString("%1/yklog_AllDbInfo.e").arg(dir_str);
  QFile yk_file(filename);
  if (!yk_file.open(QFile::WriteOnly | QFile::Text)) {
    QApplication::restoreOverrideCursor();
    printf("Cannot write file %s : %s\n", filename.toAscii().data(),
           yk_file.errorString().toAscii().data());
    return;
  }

  QMap<INT32, QString> all_ref_map;

  if (InitApp(SCADA) >= 0) {
    int tmp_size = 8;
    char* tmp_buf = (CHAR*)malloc(tmp_size);

    INT32 RetNum2 = GetAllRec(SCADA, CHANNEL_REFERENCE_NO, tmp_buf, tmp_size);
    if (RetNum2 >= 0) {
      struct channel_reference_scada* pStru = (channel_reference_scada*)tmp_buf;
      for (INT32 m = 0; m < RetNum2; m++) {
        all_ref_map.insert(pStru[m].id, pStru[m].ref);
      }
    }
    free(tmp_buf);
  }

  yk_db_list.clear();
  yk_db_map.clear();
  CDbMap cd;
  TableOp tmp;

  if (InitApp(SCADA) >= 0) {
    yk_yx_rely_scada* m_stru;
    INT32 size = 8;
    CHAR* tmpstr = (CHAR*)malloc(size);
    INT32 yk_num = GetAllRec(SCADA, YK_YX_RELY_NO, tmpstr, size);
    if (yk_num < 0) {
      printf("GetAllRec YK_YX_RELY_NO error\n");
      free(tmpstr);
      return;
    }
    m_stru = (yk_yx_rely_scada*)tmpstr;
    qDebug() << "yk_num" << yk_num;
    for (INT32 i = 0; i < yk_num; i++) {
      if (m_stru[i].sync_mode == 0) continue;
      // if (m_stru[i].sync_mode != 1 && m_stru[i].sync_mode != 2 &&
      //     m_stru[i].sync_mode != 3)
      //  continue;
      qDebug() << "yk_id" << m_stru[i].yk_id;
      INT32 tmp_size = 4;
      CHAR* tmp_buf = (CHAR*)malloc(tmp_size);

      INT32 dev_id = cd.GetDevIdxByIdx(m_stru[i].yk_id);

      INT32 num = cd.GetAllRecordByDev(YX_DEFINE_NO, dev_id, tmp_buf, tmp_size);
      if (num > 0) {
        bool get_cb_position = false;
        if (tmp.TableOpen(SCADA, YX_DEFINE_NO) >= 0) {
          INT32 tmp_idx, ret;
          struct yx_define_scada yx_stru;
          for (INT32 i = 0; i < num; i++) {
            ret = -1;
            tmp_idx = ((INT32*)tmp_buf)[i];
            ret = tmp.TableGet((CHAR*)&tmp_idx, (CHAR*)&yx_stru,
                               sizeof(yx_define_scada));
            if (ret < 0 || yx_stru.yx_type != 1) continue;
            get_cb_position = true;
            break;
          }

          tmp.TableClose();
        }
        free(tmp_buf);
        if (get_cb_position) {
          qDebug() << "append yk_id" << m_stru[i].yk_id;
          yk_db_list.append(m_stru[i].yk_id);
        }
      }
    }
    free(tmpstr);
  }

  for (int i = 0; i < yk_db_list.count(); i++) {
    yk_op_stru tmp_op_stru;
    tmp_op_stru.yk_id = yk_db_list.at(i);

    char yk_name[256];
    cd.GetNameByIdx(yk_db_list.at(i), yk_name, 256);
    strcpy(tmp_op_stru.yk_name, yk_name);

    int dev_id = cd.GetDevIdxByIdx(yk_db_list.at(i));
    tmp_op_stru.dev_id = dev_id;

    struct yk_yx_rely_scada pStru;
    if (LdbGetRecordById(YK_YX_RELY_NO, (CHAR*)&tmp_op_stru.yk_id,
                         (CHAR*)&pStru, sizeof(yk_yx_rely_scada)) >= 0) {
      tmp_op_stru.sync_mode = pStru.sync_mode;
    }

    if (all_ref_map.contains(tmp_op_stru.yk_id)) {
      tmp_op_stru.yk_ref = all_ref_map.value(tmp_op_stru.yk_id);
    } else {
      tmp_op_stru.yk_ref = "null";
    }
    tmp_op_stru.is_check = 1;
    yk_db_map.insert(tmp_op_stru.yk_id, tmp_op_stru);
  }
  qDebug() << "----------------------------------------------------------------"
              "---------------start YK count"
           << yk_db_map.count();
  open_result_buf();
  checkYk(yk_file, yk_db_map);

  QString file_make_time =
      QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
  QString cmdStr=QString("cp %1/data/yklog_AllDbInfo.e "
                         "%1/data/yklog_AllDbInfo_%s.e").arg(Common::storePath());
  char cmd[256];
  sprintf(cmd,
          cmdStr.toLocal8Bit().constData(),
          file_make_time.toAscii().data());
  qDebug() << "cmd" << cmd;
  system(cmd);
  WangXuan::GetAnswer = 1;

  yk_file.close();
}
