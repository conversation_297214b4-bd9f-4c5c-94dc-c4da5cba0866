#ifndef HV_UPDATE_TEST_H
#define HV_UPDATE_TEST_H

#include <QtGui>

#include "share_lib.h"

struct yk_op_stru {
  int yk_id;
  char yk_name[256];
  QString yk_ref;
  int dev_id;
  int sync_mode;
  int state;
  int result;
  int is_check;
};

bool convertAllPic();

bool convertSinglePic();

class CPicSimuYkWidget : public QWidget {
  Q_OBJECT
 public:
  CPicSimuYkWidget(QWidget *parent = 0);
  ~CPicSimuYkWidget();

 protected:
  // void timerEvent(QTimerEvent *);
 private:
  QTableWidget *m_table_widget;
  // QList<INT32> yk_pic_list;
  // QMap<INT32, yk_op_stru> yk_pic_map;
};

class CDbSimuYkWidget : public QWidget {
  Q_OBJECT
 public:
  CDbSimuYkWidget(QWidget *parent = 0);
  ~CDbSimuYkWidget();

 protected:
  // void timerEvent(QTimerEvent *);
 private:
  QTableWidget *m_table_widget;
  // QList<INT32> yk_db_list;
  // QMap<INT32, yk_op_stru> yk_db_map;
};

namespace WangXuan
{

class WaitDlg : public QDialog
{
    Q_OBJECT

public:
    WaitDlg( QWidget *parent = 0 );
    WaitDlg( int TimOut = 30,QWidget *parent = 0 );

    int *OverFlag;
    int theTimeFlag;
    int time_out;
    bool timeover;
    QString msg;

  QList<INT32> yk_pic_list;
  QMap<INT32, yk_op_stru> yk_pic_map;

protected:
   	QLabel *pic;
	void timerEvent( QTimerEvent * );
	int timer;
  //void run();

protected slots:
 	void	Quit();
};

}

class PicThread : public QThread {
  Q_OBJECT
 public:
  explicit PicThread();

  QList<INT32> yk_pic_list;
  QMap<INT32, yk_op_stru> yk_pic_map;

 protected:
  void run();
};

class DbThread : public QThread {
  Q_OBJECT
 public:
  explicit DbThread();

  QList<INT32> yk_db_list;
  QMap<INT32, yk_op_stru> yk_db_map;

 protected:
  void run();
};

#endif
