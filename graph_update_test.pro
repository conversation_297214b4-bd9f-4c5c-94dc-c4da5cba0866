######################################################################
# Automatically generated by qmake (2.01a) ?? ?? 28 15:17:28 2008
######################################################################

#DEPENDPATH += .
#SRCDIR = ../..
INCLUDEPATH += . $${SRCDIR}/src.graph/graph/include

#DESTDIR =  $${SRCDIR}/../exe
#INSTALLS += target
#target.path = $${SRCDIR}/../exe

LIBS += -L$${SRCDIR}/../exe -L$${SRCDIR}/lib/$(PLATFORM) -lgraph_update_test -lfront_lib
# -L$${SRCDIR}/src.common/3rd/lib/$(PLATFORM)
win32 {
	DEFINES += WIN32 _WIN32 _MBCS _USE_32BIT_TIME_T _CRT_SECURE_NO_WARNINGS
	DEFINES -= UNICODE
	LIBS += -lWs2_32
	LIBS -= -lrelay2_lib
	debug {
		DESTDIR = $${SRCDIR}/../exe
		LIBS += -L$${DESTDIR}
#		CONFIG += console
	}
} else {
	UNAME = $$system(uname -s)
	contains( UNAME, Linux ) {
		DEFINES += _LINUX
	}
	contains( UNAME, SunOS ) {
		DEFINES += _SUNOS
	}
	contains( UNAME, AIX ) {
		DEFINES += _IBM
	}
}

# Input
HEADERS += $$PWD/update_test_widget.h

SOURCES += $$PWD/update_test_widget.cpp
